#!/usr/bin/env python3
"""
CFEAREC对比学习超参数调优脚本
用于系统性地调优CFEAREC模型中对比学习相关的超参数
"""

import argparse
import os
import sys
from recbole.trainer import HyperTuning
from recbole.quick_start import objective_function


def main():
    parser = argparse.ArgumentParser(description='CFEAREC Contrastive Learning Hyperparameter Tuning')
    
    # 基础参数
    parser.add_argument('--dataset', '-d', type=str, default='ml-100k',
                       help='Dataset name (default: ml-100k)')
    parser.add_argument('--config_files', type=str, default='cfearec_contrastive_config.yaml',
                       help='Config files (default: cfearec_contrastive_config.yaml)')
    parser.add_argument('--params_file', type=str, default='cfearec_contrastive_hyper.test',
                       help='Hyperparameter search space file (default: cfearec_contrastive_hyper.test)')
    
    # 调优算法参数
    parser.add_argument('--algo', type=str, default='exhaustive', 
                       choices=['exhaustive', 'random', 'bayes'],
                       help='Hyperparameter optimization algorithm (default: exhaustive)')
    parser.add_argument('--max_evals', type=int, default=50,
                       help='Maximum number of evaluations (default: 50)')
    parser.add_argument('--early_stop', type=int, default=10,
                       help='Early stopping patience (default: 10)')
    
    # 输出参数
    parser.add_argument('--output_file', type=str, default='cfearec_contrastive_hyper_results.txt',
                       help='Output file for results (default: cfearec_contrastive_hyper_results.txt)')
    parser.add_argument('--display_file', type=str, default=None,
                       help='Display file for progress (optional)')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.params_file):
        print(f"❌ 参数文件不存在: {args.params_file}")
        print("请确保cfearec_contrastive_hyper.test文件存在")
        sys.exit(1)
    
    if args.config_files and not os.path.exists(args.config_files):
        print(f"❌ 配置文件不存在: {args.config_files}")
        print("请确保cfearec_contrastive_config.yaml文件存在")
        sys.exit(1)
    
    print("🚀 开始CFEAREC对比学习超参数调优")
    print(f"📊 数据集: {args.dataset}")
    print(f"⚙️ 配置文件: {args.config_files}")
    print(f"🔍 参数搜索空间: {args.params_file}")
    print(f"🧠 优化算法: {args.algo}")
    print(f"📈 最大评估次数: {args.max_evals}")
    print("-" * 50)
    
    # 配置文件列表
    config_file_list = args.config_files.strip().split(' ') if args.config_files else None
    
    # 创建超参数调优对象
    hp = HyperTuning(
        objective_function,
        algo=args.algo,
        early_stop=args.early_stop,
        max_evals=args.max_evals if args.algo != 'exhaustive' else None,
        params_file=args.params_file,
        fixed_config_file_list=config_file_list,
        display_file=args.display_file,
    )
    
    # 运行超参数调优
    try:
        hp.run()
        hp.export_result(output_file=args.output_file)
        
        print("\n" + "=" * 50)
        print("🎉 超参数调优完成!")
        print(f"📄 结果已保存到: {args.output_file}")
        print("\n🏆 最佳参数:")
        for param, value in hp.best_params.items():
            print(f"   {param}: {value}")
        
        print("\n📊 最佳结果:")
        best_result = hp.params2result[hp.params2str(hp.best_params)]
        for metric, value in best_result.items():
            if isinstance(value, float):
                print(f"   {metric}: {value:.4f}")
            else:
                print(f"   {metric}: {value}")
        
    except Exception as e:
        print(f"❌ 超参数调优过程中出现错误: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
