#!/usr/bin/env python3
"""
CFEAREC对比学习实验结果分析脚本
分析不同参数组合的性能表现，找出最优参数
"""

import argparse
import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
import re


def parse_log_file(log_file_path):
    """解析实验日志文件，提取性能指标"""
    metrics = {}
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取测试结果
        test_pattern = r'test result: ({[^}]+})'
        test_matches = re.findall(test_pattern, content)
        
        if test_matches:
            # 取最后一个测试结果
            test_result_str = test_matches[-1]
            # 解析指标
            recall_match = re.search(r"'recall@(\d+)': ([\d.]+)", test_result_str)
            ndcg_match = re.search(r"'ndcg@(\d+)': ([\d.]+)", test_result_str)
            
            if recall_match:
                metrics[f'recall@{recall_match.group(1)}'] = float(recall_match.group(2))
            if ndcg_match:
                metrics[f'ndcg@{ndcg_match.group(1)}'] = float(ndcg_match.group(2))
    
    except Exception as e:
        print(f"解析日志文件失败 {log_file_path}: {e}")
    
    return metrics


def analyze_experiments(experiment_dir):
    """分析实验结果"""
    
    results = []
    
    # 遍历所有实验目录
    for exp_dir in Path(experiment_dir).iterdir():
        if not exp_dir.is_dir():
            continue
        
        # 读取实验信息
        info_file = exp_dir / 'experiment_info.json'
        log_file = exp_dir / 'output.log'
        
        if not info_file.exists():
            continue
        
        try:
            with open(info_file, 'r') as f:
                exp_info = json.load(f)
            
            # 解析日志文件获取性能指标
            if log_file.exists():
                metrics = parse_log_file(log_file)
                exp_info.update(metrics)
            
            results.append(exp_info)
            
        except Exception as e:
            print(f"处理实验目录失败 {exp_dir}: {e}")
    
    return results


def create_analysis_report(results, output_dir):
    """创建分析报告"""
    
    if not results:
        print("❌ 没有找到有效的实验结果")
        return
    
    # 转换为DataFrame
    df_data = []
    for result in results:
        if not result.get('success', False):
            continue
        
        row = {
            'dataset': result.get('dataset', 'unknown'),
            'contrastive_weight': result.get('parameters', {}).get('contrastive_weight', 0),
            'contrastive_temperature': result.get('parameters', {}).get('contrastive_temperature', 0),
            'spectrum_weight': result.get('parameters', {}).get('spectrum_analysis_weight', 0),
            'filter_strength': result.get('parameters', {}).get('adaptive_filter_strength', 0),
            'duration': result.get('duration_seconds', 0),
        }
        
        # 添加性能指标
        for key, value in result.items():
            if key.startswith(('recall@', 'ndcg@', 'mrr@', 'hit@', 'precision@')):
                row[key] = value
        
        df_data.append(row)
    
    if not df_data:
        print("❌ 没有找到成功的实验结果")
        return
    
    df = pd.DataFrame(df_data)
    
    # 保存详细结果
    results_file = os.path.join(output_dir, 'detailed_results.csv')
    df.to_csv(results_file, index=False)
    print(f"📊 详细结果已保存到: {results_file}")
    
    # 分析最佳参数
    print("\n" + "="*60)
    print("📈 性能分析")
    
    # 对每个数据集和指标找出最佳参数
    for dataset in df['dataset'].unique():
        print(f"\n📊 数据集: {dataset}")
        dataset_df = df[df['dataset'] == dataset]
        
        for metric in df.columns:
            if metric.startswith(('ndcg@', 'hit@')):
                if metric in dataset_df.columns and not dataset_df[metric].isna().all():
                    best_idx = dataset_df[metric].idxmax()
                    best_row = dataset_df.loc[best_idx]
                    
                    print(f"  🏆 最佳 {metric}: {best_row[metric]:.4f}")
                    print(f"     - contrastive_weight: {best_row['contrastive_weight']}")
                    print(f"     - contrastive_temperature: {best_row['contrastive_temperature']}")
                    print(f"     - spectrum_weight: {best_row['spectrum_weight']}")
                    print(f"     - filter_strength: {best_row['filter_strength']}")
    
    # 参数敏感性分析
    print("\n" + "="*60)
    print("🔍 参数敏感性分析")
    
    param_columns = ['contrastive_weight', 'contrastive_temperature', 'spectrum_weight', 'filter_strength']
    
    for param in param_columns:
        if param in df.columns:
            print(f"\n📊 {param} 的影响:")
            param_analysis = df.groupby(param).agg({
                col: 'mean' for col in df.columns 
                if col.startswith(('ndcg@', 'hit@'))
            }).round(4)
            
            if not param_analysis.empty:
                print(param_analysis)
    
    # 创建可视化图表
    create_visualizations(df, output_dir)


def create_visualizations(df, output_dir):
    """创建可视化图表"""
    
    plt.style.use('seaborn-v0_8')
    
    # 参数vs性能的热力图
    param_columns = ['contrastive_weight', 'contrastive_temperature', 'spectrum_weight', 'filter_strength']
    metric_columns = [col for col in df.columns if col.startswith(('recall@', 'ndcg@'))]
    
    if len(metric_columns) > 0:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        for i, param in enumerate(param_columns[:4]):
            if param in df.columns and i < len(axes):
                # 计算参数值与性能指标的相关性
                param_perf = df.groupby(param)[metric_columns].mean()
                
                if not param_perf.empty:
                    sns.heatmap(param_perf.T, annot=True, fmt='.4f', 
                              cmap='YlOrRd', ax=axes[i])
                    axes[i].set_title(f'{param} vs Performance')
                    axes[i].set_xlabel(param)
                    axes[i].set_ylabel('Metrics')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'parameter_performance_heatmap.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 参数性能热力图已保存到: {os.path.join(output_dir, 'parameter_performance_heatmap.png')}")


def main():
    parser = argparse.ArgumentParser(description='Analyze CFEAREC Contrastive Learning Experiment Results')
    
    parser.add_argument('--experiment_dir', type=str, default='cfearec_experiments',
                       help='Directory containing experiment results')
    parser.add_argument('--output_dir', type=str, default='analysis_results',
                       help='Output directory for analysis results')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("🔍 开始分析CFEAREC对比学习实验结果")
    print(f"📁 实验目录: {args.experiment_dir}")
    print(f"📁 输出目录: {args.output_dir}")
    print("-" * 50)
    
    # 分析实验结果
    results = analyze_experiments(args.experiment_dir)
    
    if results:
        print(f"📊 找到 {len(results)} 个实验结果")
        create_analysis_report(results, args.output_dir)
    else:
        print("❌ 没有找到实验结果")


if __name__ == '__main__':
    main()
