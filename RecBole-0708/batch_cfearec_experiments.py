#!/usr/bin/env python3
"""
CFEAREC对比学习批量实验脚本
在多个数据集上系统性地测试不同的对比学习参数组合
"""

import argparse
import os
import subprocess
import time
from datetime import datetime
import json


def run_experiment(dataset, contrastive_weight, contrastive_temperature, 
                  spectrum_weight, filter_strength, output_dir):
    """运行单个实验"""
    
    # 创建实验配置
    exp_name = f"{dataset}_cw{contrastive_weight}_ct{contrastive_temperature}_sw{spectrum_weight}_fs{filter_strength}"
    exp_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 构建命令
    cmd = [
        'python', 'run_cfearec_contrastive.py',
        '--dataset', dataset,
        '--enable_contrastive',
        '--contrastive_weight', str(contrastive_weight),
        '--contrastive_temperature', str(contrastive_temperature),
        '--epochs', '100',  # 减少epochs以加快实验速度
        '--learning_rate', '0.001',
        '--batch_size', '256'
    ]
    
    print(f"🧪 运行实验: {exp_name}")
    print(f"📅 时间: {exp_time}")
    print(f"🔧 命令: {' '.join(cmd)}")
    
    # 创建输出目录
    exp_output_dir = os.path.join(output_dir, f"{exp_name}_{exp_time}")
    os.makedirs(exp_output_dir, exist_ok=True)
    
    # 运行实验
    start_time = time.time()
    try:
        with open(os.path.join(exp_output_dir, 'output.log'), 'w') as f:
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.STDOUT, 
                                  text=True, timeout=3600)  # 1小时超时
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 保存实验信息
        exp_info = {
            'experiment_name': exp_name,
            'dataset': dataset,
            'parameters': {
                'contrastive_weight': contrastive_weight,
                'contrastive_temperature': contrastive_temperature,
                'spectrum_analysis_weight': spectrum_weight,
                'adaptive_filter_strength': filter_strength
            },
            'start_time': exp_time,
            'duration_seconds': duration,
            'return_code': result.returncode,
            'success': result.returncode == 0
        }
        
        with open(os.path.join(exp_output_dir, 'experiment_info.json'), 'w') as f:
            json.dump(exp_info, f, indent=2)
        
        if result.returncode == 0:
            print(f"✅ 实验成功完成，耗时: {duration:.2f}秒")
        else:
            print(f"❌ 实验失败，返回码: {result.returncode}")
        
        return exp_info
        
    except subprocess.TimeoutExpired:
        print(f"⏰ 实验超时 (>1小时)")
        return {
            'experiment_name': exp_name,
            'success': False,
            'error': 'timeout'
        }
    except Exception as e:
        print(f"❌ 实验出错: {str(e)}")
        return {
            'experiment_name': exp_name,
            'success': False,
            'error': str(e)
        }


def main():
    parser = argparse.ArgumentParser(description='CFEAREC Contrastive Learning Batch Experiments')
    
    parser.add_argument('--datasets', nargs='+', 
                       default=['ml-100k'],
                       help='Datasets to experiment on')
    parser.add_argument('--output_dir', type=str, default='cfearec_experiments',
                       help='Output directory for experiments')
    parser.add_argument('--quick_test', action='store_true',
                       help='Run quick test with limited parameter combinations')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 定义参数搜索空间
    if args.quick_test:
        # 快速测试：较少的参数组合
        contrastive_weights = [0.05, 0.1, 0.2]
        contrastive_temperatures = [0.05, 0.07, 0.1]
        spectrum_weights = [0.05, 0.1]
        filter_strengths = [1.0, 1.5]
    else:
        # 完整搜索空间
        contrastive_weights = [0.01, 0.05, 0.1, 0.2, 0.5]
        contrastive_temperatures = [0.01, 0.05, 0.07, 0.1, 0.2]
        spectrum_weights = [0.01, 0.05, 0.1, 0.2]
        filter_strengths = [0.5, 1.0, 1.5, 2.0]
    
    print("🚀 开始CFEAREC对比学习批量实验")
    print(f"📊 数据集: {args.datasets}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"⚡ 快速测试模式: {args.quick_test}")
    print(f"🔢 参数组合数量: {len(contrastive_weights) * len(contrastive_temperatures) * len(spectrum_weights) * len(filter_strengths)}")
    print("-" * 60)
    
    all_results = []
    total_experiments = len(args.datasets) * len(contrastive_weights) * len(contrastive_temperatures) * len(spectrum_weights) * len(filter_strengths)
    current_exp = 0
    
    for dataset in args.datasets:
        print(f"\n📊 开始数据集: {dataset}")
        
        for cw in contrastive_weights:
            for ct in contrastive_temperatures:
                for sw in spectrum_weights:
                    for fs in filter_strengths:
                        current_exp += 1
                        print(f"\n[{current_exp}/{total_experiments}] ", end="")
                        
                        result = run_experiment(dataset, cw, ct, sw, fs, args.output_dir)
                        all_results.append(result)
    
    # 保存所有实验结果
    summary_file = os.path.join(args.output_dir, f'experiment_summary_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
    with open(summary_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    # 统计结果
    successful_experiments = [r for r in all_results if r.get('success', False)]
    failed_experiments = [r for r in all_results if not r.get('success', False)]
    
    print("\n" + "=" * 60)
    print("📊 实验总结")
    print(f"✅ 成功实验: {len(successful_experiments)}")
    print(f"❌ 失败实验: {len(failed_experiments)}")
    print(f"📄 详细结果保存在: {summary_file}")
    
    if failed_experiments:
        print("\n❌ 失败的实验:")
        for exp in failed_experiments:
            print(f"   - {exp['experiment_name']}: {exp.get('error', 'unknown error')}")


if __name__ == '__main__':
    main()
