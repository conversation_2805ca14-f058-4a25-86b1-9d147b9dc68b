# CFEAREC对比学习参数优化指南

## 📋 概述

本指南提供了系统性优化CFEAREC模型中对比学习参数的方法和工具。通过科学的实验设计，帮助你找到最优的参数组合。

## 🎯 核心参数说明

### 对比学习参数

| 参数名 | 默认值 | 建议范围 | 说明 |
|--------|--------|----------|------|
| `contrastive_weight` | 0.1 | [0.01, 0.05, 0.1, 0.2, 0.5, 1.0] | 对比学习损失权重 |
| `contrastive_temperature` | 0.07 | [0.01, 0.05, 0.07, 0.1, 0.2, 0.5] | InfoNCE温度参数 |
| `spectrum_analysis_weight` | 0.05 | [0.01, 0.05, 0.1, 0.2] | 频谱分析正则化权重 |
| `adaptive_filter_strength` | 1.0 | [0.5, 1.0, 1.5, 2.0] | 自适应滤波强度 |

### 基础模型参数

| 参数名 | 建议范围 | 说明 |
|--------|----------|------|
| `learning_rate` | [0.0001, 0.001, 0.01] | 学习率 |
| `embedding_size` | [64, 96, 128] | 嵌入维度 |
| `hidden_size` | [64, 96, 128] | 隐藏层维度 |
| `train_batch_size` | [256, 512, 1024] | 训练批次大小 |

## 🛠️ 使用方法

### 方法1: 自动超参数调优

使用RecBole的HyperTuning框架进行自动调优：

```bash
# 运行超参数调优
python run_cfearec_hyper_tune.py \
    --dataset ml-100k \
    --algo exhaustive \
    --max_evals 50 \
    --output_file results.txt
```

### 方法2: 批量实验

在多个数据集上进行系统性实验：

```bash
# 快速测试（较少参数组合）
python batch_cfearec_experiments.py \
    --datasets ml-100k Amazon_Baby_Products \
    --quick_test \
    --output_dir experiments

# 完整实验（所有参数组合）
python batch_cfearec_experiments.py \
    --datasets ml-100k Amazon_Baby_Products \
    --output_dir experiments
```

### 方法3: 手动单次实验

直接运行单个实验：

```bash
python run_cfearec_contrastive.py \
    --dataset ml-100k \
    --enable_contrastive \
    --contrastive_weight 0.1 \
    --contrastive_temperature 0.07 \
    --epochs 100
```

## 📊 结果分析

使用分析脚本处理实验结果：

```bash
python analyze_cfearec_results.py \
    --experiment_dir experiments \
    --output_dir analysis_results
```

分析脚本会生成：
- 详细结果CSV文件
- 最佳参数组合报告
- 参数敏感性分析
- 可视化图表

## 🎯 优化策略建议

### 1. 分阶段优化

**阶段1: 粗调**
- 使用较大的参数搜索范围
- 较少的训练轮数（50-100 epochs）
- 快速筛选有效的参数区间

**阶段2: 精调**
- 在有效区间内细化搜索
- 增加训练轮数（200-300 epochs）
- 使用更严格的早停策略

### 2. 参数优先级

根据经验，参数重要性排序：
1. `contrastive_weight` - 对性能影响最大
2. `contrastive_temperature` - 影响对比学习效果
3. `learning_rate` - 基础优化参数
4. `spectrum_analysis_weight` - 频谱分析权重
5. `adaptive_filter_strength` - 滤波强度

### 3. 数据集特异性

不同数据集可能需要不同的参数：

**稀疏数据集** (如ml-100k):
- 较小的`contrastive_weight` (0.01-0.1)
- 较高的`contrastive_temperature` (0.1-0.2)

**密集数据集** (如Amazon系列):
- 较大的`contrastive_weight` (0.1-0.5)
- 较低的`contrastive_temperature` (0.01-0.07)

## 📈 性能评估指标

主要关注以下指标：
- **Recall@10**: 召回率
- **NDCG@10**: 归一化折损累积增益
- **MRR@10**: 平均倒数排名
- **Hit@10**: 命中率

## ⚠️ 注意事项

1. **计算资源**: 完整的参数搜索可能需要大量计算时间
2. **早停策略**: 使用合适的早停避免过拟合
3. **随机种子**: 固定随机种子确保结果可复现
4. **验证集**: 确保有足够的验证数据进行参数选择

## 🔧 故障排除

### 常见问题

**Q: 对比学习损失不收敛**
A: 尝试减小`contrastive_weight`或增大`contrastive_temperature`

**Q: 性能提升不明显**
A: 检查数据集是否适合对比学习，尝试调整`spectrum_analysis_weight`

**Q: 训练时间过长**
A: 减少`max_evals`或使用`quick_test`模式

**Q: 内存不足**
A: 减小`train_batch_size`或`embedding_size`

## 📚 参考资料

- [RecBole超参数调优文档](https://recbole.io/docs/user_guide/usage/parameter_tuning.html)
- [CFEAREC模型论文](链接待补充)
- [对比学习在推荐系统中的应用](链接待补充)

## 🤝 贡献

如果你发现更好的参数组合或优化策略，欢迎分享！
